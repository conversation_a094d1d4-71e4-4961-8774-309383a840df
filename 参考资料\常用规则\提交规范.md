---
description: 
globs: 
alwaysApply: false
---
# Git 协同与提交规范

基于 PromptX 工作机制的智能化 Git 协同规范，通过三层架构和极简流程，实现高效、标准化的代码协作。

## ⚠️ 重要声明

**本规范仅提供指导和建议，不会自动执行任何Git操作**
- ✅ 提供标准化的提交信息格式指导
- ✅ 智能分析和建议最佳实践
- ✅ 辅助生成符合规范的提交模板
- ❌ **绝不自动执行** `git commit`、`git push`、`git merge` 等操作
- ❌ **绝不修改** 您的代码仓库状态
- ❌ **绝不替代** 您的人工决策和确认

## 一、核心理念：智能化三层架构

### 🎯 意图层 (Personality)
- **变更意图识别**：自动识别代码变更的核心目的和业务价值
- **影响范围评估**：智能分析变更对系统的影响范围和风险等级
- **协作上下文**：理解团队协作需求和项目发展阶段

### 📋 规范层 (Principle)
- **标准化格式**：遵循约定式提交规范的结构化表达
- **智能模板**：根据变更类型自动生成最适合的提交信息模板
- **质量保证**：确保提交信息的完整性、准确性和可追溯性

### 💾 内容层 (Knowledge)
- **变更记录**：详细记录代码变更的技术细节和实现方案
- **历史关联**：建立与历史提交的关联关系和演进脉络
- **知识沉淀**：将提交信息作为项目知识库的重要组成部分

## 二、极简三步提交流程

### 🔍 Step 1: 智能变更识别（仅分析，不执行）
```mermaid
flowchart TD
    A[手动查看 git diff] --> B{AI辅助识别变更类型}
    B -->|新功能| C[建议: feat]
    B -->|Bug修复| D[建议: fix]
    B -->|重构| E[建议: refactor]
    B -->|文档| F[建议: docs]
    B -->|测试| G[建议: test]
    B -->|构建| H[建议: chore]
    C --> I[生成提交信息建议]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    I --> J[用户手动确认和执行]
```

### 📝 Step 2: 模板智能生成（仅建议，不执行）
基于变更识别结果，**建议**标准化提交信息模板：

```
建议的提交格式：
@!type://required + @?scope://optional: @!title://required

@?description://optional

@?changes://optional

⚠️ 请手动复制此模板并根据实际情况调整
```

### ✅ Step 3: 结果验证交付（仅指导，不执行）
- **格式验证**：提供格式检查清单，用户自行验证
- **内容完整性**：提醒关键信息检查点，用户自行确认
- **影响评估**：提供影响分析建议，用户自行评估

## 三、分支命名规范

智能化分支命名，支持自动识别和模板生成：

*   **新功能开发**: `feature/<模块>-<功能简述>`
    *   示例: `feature/publish-video-flow`
*   **Bug 修复**: `fix/<模块>-<问题简述>`
    *   示例: `fix/manager-callback-error`
*   **紧急热修复**: `hotfix/<问题简述>`
    *   示例: `hotfix/csrf-token-issue`

## 四、智能提交规范

### 🏗️ 结构化提交格式

基于引用机制的结构化提交信息：

```
@!<类型>(@?<作用域>): @!<标题>

@?<详细说明>

@?<变更列表>
1. @!<核心变更>: <具体说明>
2. @?<次要变更>: <补充说明>
```

**引用机制说明**：
- `@!` = 强制引用（必须包含）
- `@?` = 可选引用（根据变更复杂度决定）

### 🎯 智能类型识别

系统自动识别并映射到标准类型：

| 变更模式 | 自动识别 | 提交类型 | 使用场景 |
|---------|---------|---------|---------|
| 新增文件/功能 | `+` 文件，新增API | **feat** | 功能开发 |
| 修复问题 | 错误修正，异常处理 | **fix** | Bug修复 |
| 代码重构 | 结构调整，性能优化 | **refactor** | 代码优化 |
| 文档更新 | `.md`文件变更 | **docs** | 文档维护 |
| 测试相关 | `test/`目录变更 | **test** | 测试完善 |
| 构建配置 | 配置文件，依赖更新 | **chore** | 工程配置 |

### 🎨 作用域智能推荐

基于文件路径和变更内容自动推荐作用域：

```mermaid
mindmap
  root((作用域识别))
    文件路径分析
      src/components → ui
      src/api → api
      docs/ → docs
      test/ → test
    功能模块分析
      用户管理 → user
      数据处理 → data
      界面交互 → ui
    影响范围分析
      单文件 → 具体模块
      多模块 → 系统级
      全局配置 → config
```

### 📚 智能示例生成

**传统提交** vs **智能化提交**：

```diff
- fix: 修复bug
+ fix(api): 修复用户登录接口数据序列化问题

+ 在用户登录验证过程中，JSON数据被重复序列化导致解析失败。
+
+ 1. 登录逻辑 (auth.py): 优化数据处理流程，避免重复序列化
+ 2. 错误处理: 增强异常捕获和日志记录机制
```

**完整示例**：
```
feat(user): 新增用户偏好设置功能

实现用户个性化配置管理，支持主题、语言、通知等偏好设置。

1. 用户模型 (models/user.py): 扩展用户表结构，新增偏好字段
2. 设置接口 (api/preferences.py): 实现偏好的CRUD操作接口
3. 前端组件 (components/Settings.vue): 创建设置页面交互组件
4. 数据迁移 (migrations/): 添加数据库结构变更脚本
```

## 五、智能代码审查

### 🤖 AI 辅助的多层审查机制（仅建议，不自动执行）

```mermaid
graph TD
    A[用户准备提交] --> B[AI提供审查建议]
    B --> C{用户查看建议}
    C -->|接受建议| D[用户手动修改]
    C -->|忽略建议| E[用户直接提交]
    D --> F[用户手动提交]
    E --> F
    F --> G[用户决定是否合并]
```

### 📋 智能审查模板

**基于 PromptX 的结构化审查**：

```
@!审查目标: 全面评估代码变更的质量和影响

@!变更分析:
- 代码变更: [自动提取 git diff]
- 影响范围: [智能识别影响的模块和功能]
- 风险评估: [评估潜在风险等级]

@!审查维度:
1. @!功能正确性: 逻辑错误、边界条件、异常处理
2. @!性能影响: 算法复杂度、资源消耗、性能瓶颈
3. @!代码质量: 规范遵循、可读性、可维护性
4. @!安全性: 安全漏洞、数据保护、权限控制
5. @!架构一致性: 设计模式、模块耦合、接口设计

@?优化建议: [基于历史最佳实践的改进建议]
```

### 🎯 智能问题分级

| 等级 | 标识 | 处理策略 | 示例 |
|------|------|---------|------|
| 🔴 高危 | CRITICAL | 必须修复 | 安全漏洞、数据丢失风险 |
| 🟡 中等 | MAJOR | 建议修复 | 性能问题、代码规范 |
| 🟢 轻微 | MINOR | 可选修复 | 命名优化、注释完善 |

## 六、智能合并策略

### 🔄 基于记忆管理的合并策略建议（仅建议，不执行）

```mermaid
graph LR
    A[用户考虑合并] --> B[AI分析历史模式]
    B --> C{AI建议策略}
    C -->|功能分支| D[建议: Squash Merge]
    C -->|发布分支| E[建议: Merge Commit]
    C -->|热修复| F[建议: Fast Forward]
    D --> G[AI生成提交信息建议]
    E --> G
    F --> G
    G --> H[用户手动选择和执行]
```

### 📚 策略记忆库

**基于项目历史的智能决策**：

| 场景 | 策略 | 记忆依据 | 自动化程度 |
|------|------|---------|-----------|
| 功能开发 → dev | Squash Merge | 保持dev分支简洁 | 🤖 全自动 |
| dev → main | Merge Commit | 保留发布历史 | 🔍 半自动 |
| 紧急修复 | Fast Forward | 快速部署需求 | ⚡ 智能推荐 |

### 🎯 智能提交信息继承

合并时自动生成标准化提交信息：

```
merge(release): 合并 v2.1.0 功能开发分支

本次发布包含以下核心功能和改进：

1. @!新增功能: [自动汇总 feat 类型提交]
2. @!问题修复: [自动汇总 fix 类型提交]
3. @!性能优化: [自动汇总 refactor 类型提交]
4. @?其他变更: [汇总其他类型提交]

影响范围: [智能分析影响的模块和功能]
测试覆盖: [关联的测试用例和覆盖率]
```

---

## 🚀 AI 工具集成

### Cursor 集成
- **一键生成**: `Ctrl+M` / `Cmd+M` 智能生成符合规范的提交信息
- **智能审查**: 自动代码质量检查和优化建议
- **冲突解决**: AI 辅助合并冲突处理

### 自动化工具
- **Pre-commit Hook**: 提交前自动格式验证
- **CI/CD 集成**: 自动化测试和部署流程
- **智能通知**: 基于变更影响的智能通知机制

---

**⚠️ 重要提醒**: 本规范专注于标准化和智能化，不涉及具体的 Git 操作执行。


